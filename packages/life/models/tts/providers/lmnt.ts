import Lmnt from "lmnt-node";
import { z } from "zod";
import { TTSBase, type TTSGenerateJob } from "../base";

// Config
export const lmntTTSConfigSchema = z.object({
  apiKey: z.string().default(process.env.LMNT_API_KEY ?? ""),
  voice: z.string().default("lily"), // Default voice
  speed: z.number().min(0.25).max(2.0).default(1.0),
  format: z.enum(["mp3", "raw", "ulaw"]).default("raw"), // Use raw PCM for Life.js compatibility
  sampleRate: z.union([z.literal(8000), z.literal(16000), z.literal(24000)]).default(16000), // 16kHz matches Life.js standard
  language: z.enum(["de", "en", "es", "fr", "pt", "zh", "ko", "hi"]).default("en"),
  returnExtras: z.boolean().default(false),
});

// Model
export class LmntTTS extends TTSBase<typeof lmntTTSConfigSchema> {
  #client: Lmnt;
  #activeSessions: Map<string, any> = new Map();

  constructor(config: z.input<typeof lmntTTSConfigSchema>) {
    super(lmntTTSConfigSchema, config);
    
    if (!this.config.apiKey) {
      throw new Error(
        "LMNT_API_KEY environment variable or config.apiKey must be provided to use this model."
      );
    }

    this.#client = new Lmnt({
      apiKey: this.config.apiKey,
    });
  }

  async generate(): Promise<TTSGenerateJob> {
    // Create a new generation job
    const job = this.createGenerateJob();

    // Create a speech session for streaming
    const speechSession = this.#client.speech.sessions.create({
      voice: this.config.voice,
      format: this.config.format,
      sample_rate: this.config.sampleRate,
      language: this.config.language,
      return_extras: this.config.returnExtras,
    });

    // Store the session for this job
    this.#activeSessions.set(job.id, speechSession);

    // Listen to job cancellation and properly close the session
    job.raw.abortController.signal.addEventListener("abort", () => {
      const session = this.#activeSessions.get(job.id);
      if (session) {
        session.close();
        this.#activeSessions.delete(job.id);
      }
    });

    // Start listening for audio chunks from LMNT
    this.#startListening(job, speechSession);

    return job;
  }

  protected async _onGeneratePushText(job: TTSGenerateJob, text: string): Promise<void> {
    const session = this.#activeSessions.get(job.id);
    if (!session) {
      console.warn(`No active session found for job ${job.id}`);
      return;
    }

    try {
      // Send text to LMNT for synthesis
      await session.appendText(text);
      
      // Flush to ensure the text gets processed
      // Note: We don't call finish() here as we want to keep the session open for more text
      await session.flush();
    } catch (error) {
      console.error("Error sending text to LMNT:", error);
      job.raw.receiveChunk({ 
        type: "error", 
        error: error instanceof Error ? error.message : "Unknown error" 
      });
    }
  }

  async #startListening(job: TTSGenerateJob, speechSession: any): Promise<void> {
    try {
      // Listen for audio chunks from the session
      for await (const message of speechSession) {
        // If the job has been aborted, stop processing
        if (job.raw.abortController.signal.aborted) {
          break;
        }

        if (message.audio) {
          // LMNT raw format provides 16-bit little-endian PCM, convert to Int16Array
          const pcmData = this.#convertRawPcmToInt16Array(message.audio);

          job.raw.receiveChunk({
            type: "content",
            voiceChunk: pcmData
          });
        }
      }

      // Signal end of stream
      if (!job.raw.abortController.signal.aborted) {
        job.raw.receiveChunk({ type: "end" });
      }

      // Clean up the session
      this.#activeSessions.delete(job.id);
      
    } catch (error) {
      console.error("Error in LMNT session:", error);
      job.raw.receiveChunk({ 
        type: "error", 
        error: error instanceof Error ? error.message : "Unknown error" 
      });
      this.#activeSessions.delete(job.id);
    }
  }

  #convertRawPcmToInt16Array(rawBuffer: Buffer): Int16Array {
    // LMNT raw format provides 16-bit little-endian PCM
    // Convert Buffer to Int16Array directly
    const pcmLength = Math.floor(rawBuffer.length / 2);
    const pcmArray = new Int16Array(pcmLength);

    // Convert little-endian 16-bit PCM bytes to Int16Array
    for (let i = 0; i < pcmLength; i++) {
      const byte1 = rawBuffer[i * 2] || 0;     // Low byte
      const byte2 = rawBuffer[i * 2 + 1] || 0; // High byte
      pcmArray[i] = (byte2 << 8) | byte1;     // Little-endian conversion
    }

    return pcmArray;
  }
}
